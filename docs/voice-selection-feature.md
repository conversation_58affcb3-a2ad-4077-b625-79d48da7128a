# Voice Selection Feature

## Overview
This feature allows users to select their preferred Text-to-Speech (TTS) voice for the application. Users can choose from the available voices on their device and their preference is saved for future use.

## Implementation Details

### 1. Voice Utilities (`src/utils/voiceUtils.ts`)
- `getAvailableVoices()`: Retrieves all available TTS voices on the device
- `getDefaultVoice()`: Returns the first available voice as default

### 2. Speech Utilities (`src/utils/speechUtils.ts`)
- `speakWithSelectedVoice()`: Speaks text using the user's selected voice
- `stopSpeech()`: Stops any ongoing speech
- `isSpeaking()`: Checks if speech is currently playing

### 3. Profile Screen (`src/screens/Profile.tsx`)
- Added a "Voice Selection" button that opens a modal
- Modal displays a list of available voices
- Users can select a voice which is saved to AsyncStorage
- Selected voice is highlighted in the list

### 4. Integration with Existing Components
- Modified `chatItem.tsx` to use `speakWithSelectedVoice()` instead of direct `Speech.speak()`
- Updated `chatHooks.tsx` and `Topics.tsx` to use new speech utilities
- Updated `recordingHelper.ts` to use new speech utilities

## How It Works

1. On app startup, available voices are retrieved using `getAvailableVoices()`
2. User's voice preference is loaded from AsyncStorage
3. If no preference exists, the first available voice is used as default
4. When user selects a voice in the Profile screen, it's saved to AsyncStorage
5. When TTS is used throughout the app, `speakWithSelectedVoice()` automatically uses the saved voice

## Data Storage
Voice preferences are stored in AsyncStorage with the key "selectedVoice". The value is the voice identifier string.

## Testing
A test component (`VoiceTestComponent.tsx`) is available to verify the functionality works correctly.