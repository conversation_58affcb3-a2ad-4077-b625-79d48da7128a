import React, { useCallback, useEffect } from "react";
import { Provider } from "react-redux";
import store, { persistor } from "./src/store/store";
import TabNavigator from "./src/navigation/TabNavigator";
import { PersistGate } from "redux-persist/integration/react";
import { useFonts } from "expo-font";
import { StatusBar } from "expo-status-bar";
import Loading from "./src/components/ui/loading";
import { StyleSheet, LogBox, I18nManager } from "react-native";
import Toast, { ErrorToast, BaseToast } from "react-native-toast-message";
import { getApp } from '@react-native-firebase/app';
import { getMessaging, Messaging, AuthorizationStatus, onMessage, getToken } from '@react-native-firebase/messaging';

function App() {
  I18nManager.allowRTL(false);
  const [fontsLoaded, fontError] = useFonts({
    EstedadRegular: require("./assets/fonts/Estedad-Regular.ttf"),
    EstedadBold: require("./assets/fonts/Estedad-Bold.ttf"),
  });

  const toastConfig = {
    error: (props) => (
      <ErrorToast
        {...props}
        style={{ borderLeftColor: "red" }}
        text1Style={styles.text1}
        text2Style={styles.text2}
      />
    ),
    success: (props) => (
      <BaseToast
        {...props}
        style={{ borderLeftColor: "green" }}
        contentContainerStyle={{ paddingHorizontal: 15 }}
        text1Style={styles.text1}
      />
    ),
  };

  LogBox.ignoreAllLogs();

  async function requestUserPermission() {
    const messaging = getMessaging(getApp());
    const authStatus = await messaging.requestPermission();
    const enabled =
      authStatus === AuthorizationStatus.AUTHORIZED ||
      authStatus === AuthorizationStatus.PROVISIONAL;

    if (enabled) {
      // console.log("Authorization status:", authStatus);
    }
  }

  useEffect(() => {
    requestUserPermission();
  }, []);

  useEffect(() => {
    requestUserPermission();
  }, []);

  useEffect(() => {
    const messaging = getMessaging(getApp());
    const unsubscribe = onMessage(messaging, async (remoteMessage) => {
      console.log("A new FCM message arrived!", JSON.stringify(remoteMessage));
    });

    return unsubscribe;
  }, []);

  const getFcmToken = async () => {
    const messaging = getMessaging(getApp());
    const fcmToken = await getToken(messaging);
    if (fcmToken) {
      // console.log("Your Firebase Token:", fcmToken);
    } else {
      console.log("Failed to get token");
    }
  };

  useEffect(() => {
    getFcmToken();
  }, []);

  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <TabNavigator />
        <Loading />
      </PersistGate>
      <StatusBar style="auto" />
      <Toast config={toastConfig} />
    </Provider>
  );
}

const styles = StyleSheet.create({
  text1: {
    fontSize: 16,
    fontWeight: "normal",
    fontFamily: "EstedadRegular",
  },
  text2: {
    fontSize: 13,
    fontWeight: "normal",
    fontFamily: "EstedadRegular",
  },
});

export default App;
