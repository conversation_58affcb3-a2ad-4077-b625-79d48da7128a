import "react-native-get-random-values";
import React, { useState, useEffect } from "react";
import * as Speech from "expo-speech";
import { View } from "react-native";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import { initNewChat } from "../../store/slices/modelSlice";
// import { loadModel } from "../../utils/whisperModel";
import ChatUi from "./chatUi";
import { useChatState } from "./chatHooks";
// CongratulationsAnimation removed as we're directly showing the assessment screen
import {
  startTopic,
  chatRequest,
  requestAgain,
  submitChat,
  addReqToHistory,
} from "./chatActions";
import { handleRecordingComplete } from "../recording/handleTranscription";
import VoiceAssistantUi from "../recording/voiceAssistantUi";

interface ChatProps {
  route: any;
  navigation: any;
  onTranscriptionUpdate?: (result: any[]) => void;
}

export default function Chat({
  route,
  navigation,
  onTranscriptionUpdate,
}: ChatProps) {
  const { topicItem, comesFrom } = route.params;
  const dispatch = useAppDispatch();
  const { level, isPremium, coins } = useAppSelector((state) => state.auth);
  const [showVoiceAssistantModal, setShowVoiceAssistantModal] = useState(false);
  // Use the custom hook to manage chat state
  const {
    chatId,
    setChatId,
    inputValue,
    setInputValue,
    isSpeaking,
    setIsSpeaking,
    isModalVisible,
    setIsModalVisible,
    playSpeech,
    setPlaySpeech,
    requested,
    setRequested,
    scores,
    setScores,
    textSubmitted,
    setTextSubmitted,
    chatFinished,
    setChatFinished,
    hasReachedDailyLimit,
    setHasReachedDailyLimit,
    transcriptionResult,
    setTranscriptionResult,
    Session,
    newChat,
    approvedNewChat,
    setApprovedNewChat,
    keySize,
    // checkIfReachedLastMessage and showCongratulations removed
  } = useChatState(topicItem, navigation, comesFrom);

  // Load model if needed
  // useEffect(() => {
  //   if (!Session) {
  //     loadModel();
  //   }
  // }, []);

  // Start topic when component loads or when topicItem changes
  useEffect(() => {
    startTopic(
      newChat,
      topicItem,
      setChatId,
      setTranscriptionResult,
      setRequested,
      setScores,
      dispatch,
      initNewChat,
      keySize,
      level,
      approvedNewChat,
      isPremium,
      coins
    );
  }, [topicItem._id]);

  // Start topic when newChat changes
  useEffect(() => {
    if (approvedNewChat) {
      startTopic(
        newChat,
        topicItem,
        setChatId,
        setTranscriptionResult,
        setRequested,
        setScores,
        dispatch,
        initNewChat,
        keySize,
        level,
        approvedNewChat,
        isPremium,
        coins
      );
    }
  }, [approvedNewChat]);

  // Handle transcription results
  useEffect(() => {
    if (transcriptionResult.length) {
      // Call the callback if provided
      if (onTranscriptionUpdate) {
        onTranscriptionUpdate(transcriptionResult);
      }

      const last = transcriptionResult[transcriptionResult.length - 1];
      if (last?.type === "loading" && !requested) {
        chatRequest(
          last.key,
          transcriptionResult,
          chatId,
          topicItem,
          setTranscriptionResult,
          setPlaySpeech,
          setHasReachedDailyLimit,
          () => {}, // Empty function instead of setShowCongratulations
          level,
          navigation,
          setChatFinished
        );
      }
      // if (last?.type === "res") {
      //   const trimmedContainsQuestionMark = /\?|tell/i.test(last.text);
      //   const words = last.text.split(/\s+/);
      //   if (!trimmedContainsQuestionMark && !requested && words.length < 10) {
      //     requestAgain(
      //       transcriptionResult,
      //       chatId,
      //       topicItem,
      //       setRequested,
      //       setTranscriptionResult,
      //       setPlaySpeech,
      //       setHasReachedDailyLimit
      //     );
      //   }
      // }
    }
  }, [transcriptionResult]);

  useEffect(() => {
    if (route?.params?.showVoiceAssistant) {
      setShowVoiceAssistantModal(true);
      // Ensure a fresh conversation each time the voice assistant opens
      setApprovedNewChat(true);
    }
  }, [route?.params?.showVoiceAssistant]);

  // Handle chat submission
  const submit = () => {
    submitChat(
      inputValue,
      setTextSubmitted,
      setInputValue,
      setIsSpeaking,
      (text, isTopic) =>
        addReqToHistory(
          text,
          isTopic,
          transcriptionResult,
          setTranscriptionResult,
          setRequested
        )
    );
  };

  const recordingComplete = (uri) => {
    handleRecordingComplete(
      transcriptionResult,
      setTranscriptionResult,
      chatId,
      uri,
      setTextSubmitted
    );
  };

  return (
    <>
      {/* Voice Assistant Modal */}
      <React.Fragment>
        {showVoiceAssistantModal && (
          <View
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              zIndex: 999,
            }}
          >
            <VoiceAssistantUi
              transcriptionResult={transcriptionResult}
              setTranscriptionResult={setTranscriptionResult}
              chatId={chatId}
              isSpeaking={isSpeaking}
              setIsSpeaking={setIsSpeaking}
              setTextSubmitted={setTextSubmitted}
            />
          </View>
        )}
      </React.Fragment>
      <View style={{ flex: 1 }}>
        <ChatUi
          transcriptionResult={transcriptionResult}
          Speech={Speech}
          isSpeaking={isSpeaking}
          submit={submit}
          inputValue={inputValue}
          setInputValue={setInputValue}
          isModalVisible={isModalVisible}
          setIsModalVisible={setIsModalVisible}
          topicItem={topicItem}
          setIsSpeaking={setIsSpeaking}
          chatId={chatId}
          playSpeech={playSpeech}
          scores={scores}
          textSubmitted={textSubmitted}
          chatFinished={chatFinished}
          setChatFinished={setChatFinished}
          newChat={newChat}
          setScores={setScores}
          hasReachedDailyLimit={hasReachedDailyLimit}
          navigation={navigation}
          recordingComplete={recordingComplete}
          approvedNewChat={approvedNewChat}
          setApprovedNewChat={setApprovedNewChat}
        />
      </View>
    </>
  );
}
