import React, { useEffect, useRef } from 'react';
import { View, Text, Animated, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

interface RecordingAnimationProps {
  audioLevel: number; // Audio level in dB (-160 to 0)
  isRecording: boolean;
}

const RecordingAnimation: React.FC<RecordingAnimationProps> = ({ audioLevel, isRecording }) => {
  // Animation values
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const glowAnim = useRef(new Animated.Value(0)).current;
  const waveAnims = useRef(Array.from({ length: 5 }, () => new Animated.Value(0))).current;
  const microphoneAnim = useRef(new Animated.Value(1)).current;
  const rippleAnim = useRef(new Animated.Value(0)).current;

  // Normalize audio level to a value between 0 and 1
  const normalizedLevel = Math.min(1, Math.max(0, (audioLevel + 160) / 160));

  useEffect(() => {
    if (isRecording) {
      // Start all animations when recording begins
      startRecordingAnimations();
    } else {
      // Stop all animations when recording ends
      stopRecordingAnimations();
    }

    return () => {
      // Cleanup animations
      pulseAnim.stopAnimation();
      glowAnim.stopAnimation();
      microphoneAnim.stopAnimation();
      rippleAnim.stopAnimation();
      waveAnims.forEach(anim => anim.stopAnimation());
    };
  }, [isRecording]);

  // Update wave animations based on audio level
  useEffect(() => {
    if (isRecording && normalizedLevel > 0.1) {
      animateWaves();
    }
  }, [normalizedLevel, isRecording]);

  const startRecordingAnimations = () => {
    // Pulse animation for the main container
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Glow animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(glowAnim, {
          toValue: 1,
          duration: 1500,
          useNativeDriver: true,
        }),
        Animated.timing(glowAnim, {
          toValue: 0.3,
          duration: 1500,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Microphone bounce animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(microphoneAnim, {
          toValue: 1.05,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(microphoneAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ])
    ).start();

    // Ripple effect
    Animated.loop(
      Animated.timing(rippleAnim, {
        toValue: 1,
        duration: 2000,
        useNativeDriver: true,
      })
    ).start();
  };

  const stopRecordingAnimations = () => {
    pulseAnim.stopAnimation();
    glowAnim.stopAnimation();
    microphoneAnim.stopAnimation();
    rippleAnim.stopAnimation();
    waveAnims.forEach(anim => anim.stopAnimation());

    // Reset to initial values
    pulseAnim.setValue(1);
    glowAnim.setValue(0);
    microphoneAnim.setValue(1);
    rippleAnim.setValue(0);
    waveAnims.forEach(anim => anim.setValue(0));
  };

  const animateWaves = () => {
    // Animate waves based on audio level
    waveAnims.forEach((anim, index) => {
      const delay = index * 100;
      const intensity = normalizedLevel * (0.8 + Math.random() * 0.4);
      
      Animated.timing(anim, {
        toValue: intensity,
        duration: 200,
        useNativeDriver: true,
      }).start(() => {
        // Fade out
        Animated.timing(anim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }).start();
      });
    });
  };

  const getWaveHeight = (index: number) => {
    const baseHeight = 20 + (index * 8);
    const audioMultiplier = 1 + (normalizedLevel * 2);
    return baseHeight * audioMultiplier;
  };

  const getWaveColor = () => {
    if (normalizedLevel < 0.3) return '#4CAF50'; // Green for low levels
    if (normalizedLevel < 0.7) return '#FF9800'; // Orange for medium levels
    return '#F44336'; // Red for high levels
  };

  if (!isRecording) return null;

  return (
    <Animated.View
      style={[
        styles.container,
        {
          transform: [{ scale: pulseAnim }],
        },
      ]}
    >
      {/* Ripple effects */}
      <Animated.View
        style={[
          styles.ripple,
          styles.ripple1,
          {
            opacity: rippleAnim.interpolate({
              inputRange: [0, 0.5, 1],
              outputRange: [0.8, 0.4, 0],
            }),
            transform: [
              {
                scale: rippleAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0.8, 2.5],
                }),
              },
            ],
          },
        ]}
      />
      
      <Animated.View
        style={[
          styles.ripple,
          styles.ripple2,
          {
            opacity: rippleAnim.interpolate({
              inputRange: [0, 0.3, 0.8, 1],
              outputRange: [0, 0.6, 0.3, 0],
            }),
            transform: [
              {
                scale: rippleAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0.5, 2],
                }),
              },
            ],
          },
        ]}
      />

      {/* Glow background */}
      <Animated.View
        style={[
          styles.glowBackground,
          {
            opacity: glowAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [0.4, 0.8],
            }),
          },
        ]}
      >
        <LinearGradient
          colors={['rgba(244, 67, 54, 0.3)', 'rgba(244, 67, 54, 0.1)', 'rgba(244, 67, 54, 0.3)']}
          style={styles.glowGradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      </Animated.View>

      {/* Audio waves */}
      <View style={styles.wavesContainer}>
        {waveAnims.map((anim, index) => (
          <Animated.View
            key={index}
            style={[
              styles.wave,
              {
                height: getWaveHeight(index),
                backgroundColor: getWaveColor(),
                opacity: anim,
                transform: [
                  {
                    scaleY: anim.interpolate({
                      inputRange: [0, 1],
                      outputRange: [0.3, 1.2],
                    }),
                  },
                ],
              },
            ]}
          />
        ))}
      </View>

      {/* Microphone icon */}
      <Animated.View
        style={[
          styles.microphoneContainer,
          {
            transform: [{ scale: microphoneAnim }],
          },
        ]}
      >
        <View style={styles.microphoneIcon}>
          <Text style={styles.microphoneEmoji}>🎤</Text>
        </View>
      </Animated.View>

      {/* Recording text */}
      <Animated.View
        style={[
          styles.textContainer,
          {
            opacity: glowAnim.interpolate({
              inputRange: [0, 1],
              outputRange: [0.7, 1],
            }),
          },
        ]}
      >
        <Text style={styles.recordingText}>صحبت کن...</Text>
        <Text style={styles.levelText}>
          {normalizedLevel > 0.1 ? '🔊' : '🔇'} {(normalizedLevel * 100).toFixed(0)}%
        </Text>
      </Animated.View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 30,
    paddingHorizontal: 20,
    position: 'relative',
  },
  ripple: {
    position: 'absolute',
    borderRadius: 100,
    borderWidth: 2,
    borderColor: 'rgba(244, 67, 54, 0.6)',
  },
  ripple1: {
    width: 120,
    height: 120,
  },
  ripple2: {
    width: 160,
    height: 160,
  },
  glowBackground: {
    position: 'absolute',
    width: 140,
    height: 140,
    borderRadius: 70,
  },
  glowGradient: {
    flex: 1,
    borderRadius: 70,
  },
  wavesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 60,
    marginBottom: 20,
  },
  wave: {
    width: 4,
    marginHorizontal: 2,
    borderRadius: 2,
    shadowColor: '#F44336',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.5,
    shadowRadius: 4,
    elevation: 5,
  },
  microphoneContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 15,
    zIndex: 2,
  },
  microphoneIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(244, 67, 54, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: 'rgba(244, 67, 54, 0.5)',
  },
  microphoneEmoji: {
    fontSize: 24,
  },
  textContainer: {
    alignItems: 'center',
    zIndex: 2,
  },
  recordingText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 5,
    textShadowColor: 'rgba(244, 67, 54, 0.5)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 3,
    fontFamily: 'EstedadRegular',
  },
  levelText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    fontWeight: '500',
  },
});

export default RecordingAnimation;
