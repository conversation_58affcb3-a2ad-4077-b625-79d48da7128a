import React, { useMemo, useRef, useState } from "react";
import { View, Text, TouchableOpacity, Image } from "react-native";
import * as Speech from "expo-speech";
import MP3Recording from "./MP3Recording";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";
import styles from "../../styles/voiceAssistantStyle";
import GrammarPopup from "../chat/GrammarPopup";
import { compareTexts } from "../../utils/compareTexts";
import AudioVisualizer from "./AudioVisualizer";
import SettingsModal from "../modals/SettingsModal";
import ThinkingAnimation from "./ThinkingAnimation";
import RecordingAnimation from "./RecordingAnimation";
import {
  recordingComplete,
  selectVoice,
  useVoiceAssistantLogic,
} from "./voiceAssistantHelpers";

const avatarSource = require("../../../assets/icons/bot.jpg");

const VoiceAssistant = ({
  transcriptionResult,
  setTranscriptionResult,
  chatId,
  isSpeaking,
  setIsSpeaking,
  setTextSubmitted,
}: any) => {
  const [grammarErrorSpeechText, setGrammarErrorSpeechText] = useState<{
    text: string;
    color: string;
  } | null>(null);
  const [shouldAutoRestart, setShouldAutoRestart] = useState(false);
  const [shouldRestartAfterGrammarError, setShouldRestartAfterGrammarError] =
    useState(false);
  const [lastResponseLength, setLastResponseLength] = useState(0);
  const [audioLevel, setAudioLevel] = useState<number>(-160); // Default to minimum level
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [isVoiceModalVisible, setIsVoiceModalVisible] = useState(false);
  const [voices, setVoices] = useState<any[]>([]);
  const [selectedVoice, setSelectedVoice] = useState<string | null>(null);
  const [translation, setTranslation] = useState<string>("");
  const [isTranslating, setIsTranslating] = useState<boolean>(false);
  const mp3RecordingRef = useRef<any>(null);
  const speechTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const previousIsSpeakingRef = useRef<number | null>(null);
  let lastItem = transcriptionResult[transcriptionResult.length - 1];
  const lastUserMessage = useMemo(
    () => [...transcriptionResult].reverse().find((i: any) => i.type === "req"),
    [transcriptionResult]
  );
  const lastAssistantMessage = useMemo(
    () =>
      [...transcriptionResult]
        .reverse()
        .find((i: any) => i.type === "res" && !!i.text),
    [transcriptionResult]
  );

  // Grammar states
  const [isGrammarCorrect, setIsGrammarCorrect] = useState<boolean | null>(
    null
  );
  const [correctedText, setCorrectedText] = useState<string>("");
  const [grammarModalVisible, setGrammarModalVisible] = useState(false);
  const lastCheckedTextRef = useRef<string>("");

  useVoiceAssistantLogic(
    transcriptionResult,
    setTranscriptionResult,
    chatId,
    isSpeaking,
    setIsSpeaking,
    setTextSubmitted,
    lastResponseLength,
    setLastResponseLength,
    shouldAutoRestart,
    setShouldAutoRestart,
    shouldRestartAfterGrammarError,
    setShouldRestartAfterGrammarError,
    mp3RecordingRef,
    speechTimeoutRef,
    previousIsSpeakingRef,
    lastUserMessage,
    lastAssistantMessage,
    setIsGrammarCorrect,
    setCorrectedText,
    lastCheckedTextRef,
    setIsTranslating,
    setTranslation,
    setIsRecording,
    setVoices,
    setSelectedVoice
  );

  return (
    <LinearGradient
      colors={["#0a1535", "#1a2a5e", "#0a1535"]}
      style={styles.container}
    >
      {/* Avatar Container */}
      {/* <View style={styles.avatarContainer}>
        <View style={styles.avatarCircle}>
          <Image source={avatarSource} style={styles.avatar} />
        </View>
      </View> */}

      {/* Response Text */}
      <View style={styles.textContainer}>
        {grammarErrorSpeechText ? (
          <Text style={styles.responseText}>
            <Text style={{ color: grammarErrorSpeechText.color }}>
              {grammarErrorSpeechText.text}
            </Text>
          </Text>
        ) : (
          <>
            {(lastItem?.type === "loading" ||
              lastItem?.type === "loadingReq") && <ThinkingAnimation />}

            {/* Show recording animation when user is speaking or when processing user input */}
            {(isRecording || lastItem?.type === "req") && (
              <RecordingAnimation audioLevel={audioLevel} isRecording={isRecording || lastItem?.type === "req"} />
            )}

            {!isRecording &&
              lastItem?.type === "res" &&
              lastAssistantMessage && (
                <View>
                  <Text style={styles.responseText}>
                    {lastAssistantMessage.text}
                  </Text>
                  {translation && (
                    <View
                      style={{
                        marginTop: 10,
                        padding: 10,
                        backgroundColor: "#1e3a8a",
                        borderRadius: 5,
                      }}
                    >
                      <Text style={{ color: "white", fontSize: 16 }}>
                        {translation}
                      </Text>
                    </View>
                  )}
                </View>
              )}

            {/* Inline grammar corrections for the last user message */}
            {lastItem?.type === "res" &&
              lastUserMessage?.text &&
              !isGrammarCorrect &&
              !!correctedText && (
                <TouchableOpacity onPress={() => setGrammarModalVisible(true)}>
                  <View
                    style={{
                      flexDirection: "row",
                      flexWrap: "wrap",
                      marginTop: 8,
                      borderTopWidth: 1,
                      borderTopColor: "#ccc",
                      paddingTop: 8,
                    }}
                  >
                    {compareTexts(lastUserMessage.text, correctedText).map(
                      (segment, index, arr) => (
                        <Text
                          key={index}
                          style={{
                            fontSize: 16,
                            color: segment.isDifferent ? "#2E7D32" : "#888",
                            fontWeight: segment.isDifferent ? "bold" : "normal",
                          }}
                        >
                          {segment.text}
                          {index < arr.length - 1 ? " " : ""}
                        </Text>
                      )
                    )}
                  </View>
                </TouchableOpacity>
              )}
          </>
        )}
      </View>

      {/* Recording Animation - Alternative placement if needed */}
      {/* <RecordingAnimation audioLevel={audioLevel} isRecording={isRecording} /> */}

      {/* Control Buttons */}
      <View style={styles.controlsContainer}>
        {/* Record Button using MP3Recording */}
        <View style={{ alignItems: "center", display: "none" }}>
          <MP3Recording
            ref={mp3RecordingRef}
            isSpeaking={isSpeaking}
            setIsSpeaking={setIsSpeaking}
            Speech={Speech}
            onRecordingComplete={async (uri: string) => {
              setGrammarErrorSpeechText(null); // Clear previous error speech when a new recording starts
              await recordingComplete(
                transcriptionResult,
                setTranscriptionResult,
                chatId,
                uri,
                setTextSubmitted,
                isGrammarCorrect,
                setGrammarErrorSpeechText,
                mp3RecordingRef,
                setShouldRestartAfterGrammarError,
                setIsSpeaking
              );
            }}
            autoStart={true}
            transcriptionResult={transcriptionResult}
            onAudioLevelChange={setAudioLevel}
          />
        </View>

        <TouchableOpacity
          style={styles.controlButton}
          onPress={() => setIsVoiceModalVisible(true)}
        >
          <Ionicons name="settings-outline" size={30} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Grammar details modal */}
      {lastUserMessage?.text && !!correctedText && (
        <GrammarPopup
          visible={grammarModalVisible}
          originalText={lastUserMessage.text}
          correctedText={correctedText}
          onClose={() => setGrammarModalVisible(false)}
        />
      )}

      <SettingsModal
        visible={isVoiceModalVisible}
        onClose={() => setIsVoiceModalVisible(false)}
        onSelectVoice={(voiceId: string) =>
          selectVoice(voiceId, setSelectedVoice, setIsVoiceModalVisible)
        }
        selectedVoice={selectedVoice}
        voices={voices}
      />
    </LinearGradient>
  );
};

export default VoiceAssistant;
