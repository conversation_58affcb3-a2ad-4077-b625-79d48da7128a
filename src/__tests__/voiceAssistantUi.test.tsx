import React from 'react';
import { render, waitFor } from '@testing-library/react-native';
import VoiceAssistant from '../components/recording/voiceAssistantUi';

// Mock dependencies
jest.mock('expo-speech', () => ({
  speak: jest.fn(),
  stop: jest.fn(),
}));

jest.mock('../components/MP3Recording', () => {
  const React = require('react');
  return React.forwardRef((props: any, ref: any) => {
    React.useImperativeHandle(ref, () => ({
      restartRecording: jest.fn(),
      stopRecording: jest.fn(),
      isCurrentlyRecording: jest.fn(() => false),
    }));
    return null;
  });
});

jest.mock('../components/handleTranscription', () => ({
  handleRecordingComplete: jest.fn(),
}));

jest.mock('expo-linear-gradient', () => ({
  LinearGradient: 'LinearGradient',
}));

jest.mock('@expo/vector-icons', () => ({
  Ionicons: 'Ionicons',
}));

describe('VoiceAssistant Auto-Restart Functionality', () => {
  const mockProps = {
    transcriptionResult: [],
    setTranscriptionResult: jest.fn(),
    chatId: 'test-chat-id',
    isSpeaking: null,
    setIsSpeaking: jest.fn(),
    setTextSubmitted: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('should prepare for auto-restart when new LLM response is received', async () => {
    const { rerender } = render(<VoiceAssistant {...mockProps} />);

    // Add a new LLM response
    const newTranscriptionResult = [
      { type: 'res', text: 'Hello, how can I help you?', _id: '1' }
    ];

    rerender(
      <VoiceAssistant 
        {...mockProps} 
        transcriptionResult={newTranscriptionResult}
      />
    );

    // The component should prepare for auto-restart
    await waitFor(() => {
      expect(mockProps.setTranscriptionResult).toHaveBeenCalled();
    });
  });

  it('should restart recording when isSpeaking changes from number to null', async () => {
    const mockRestartRecording = jest.fn();
    
    // Mock the MP3Recording ref
    jest.doMock('../components/MP3Recording', () => {
      const React = require('react');
      return React.forwardRef((props: any, ref: any) => {
        React.useImperativeHandle(ref, () => ({
          restartRecording: mockRestartRecording,
          stopRecording: jest.fn(),
          isCurrentlyRecording: jest.fn(() => false),
        }));
        return null;
      });
    });

    const transcriptionResult = [
      { type: 'res', text: 'Hello, how can I help you?', _id: '1' }
    ];

    const { rerender } = render(
      <VoiceAssistant 
        {...mockProps} 
        transcriptionResult={transcriptionResult}
        isSpeaking={0} // Currently speaking
      />
    );

    // Change isSpeaking to null (speech finished)
    rerender(
      <VoiceAssistant 
        {...mockProps} 
        transcriptionResult={transcriptionResult}
        isSpeaking={null} // Speech finished
      />
    );

    // Fast-forward the timeout
    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(mockRestartRecording).toHaveBeenCalled();
    });
  });

  it('should not restart recording if no LLM response was received', async () => {
    const mockRestartRecording = jest.fn();
    
    jest.doMock('../components/MP3Recording', () => {
      const React = require('react');
      return React.forwardRef((props: any, ref: any) => {
        React.useImperativeHandle(ref, () => ({
          restartRecording: mockRestartRecording,
          stopRecording: jest.fn(),
          isCurrentlyRecording: jest.fn(() => false),
        }));
        return null;
      });
    });

    const { rerender } = render(
      <VoiceAssistant 
        {...mockProps} 
        isSpeaking={0} // Currently speaking
      />
    );

    // Change isSpeaking to null without any LLM response
    rerender(
      <VoiceAssistant 
        {...mockProps} 
        isSpeaking={null} // Speech finished
      />
    );

    // Fast-forward the timeout
    jest.advanceTimersByTime(1000);

    await waitFor(() => {
      expect(mockRestartRecording).not.toHaveBeenCalled();
    });
  });

  it('should clean up timeout on unmount', () => {
    const clearTimeoutSpy = jest.spyOn(global, 'clearTimeout');
    
    const { unmount } = render(<VoiceAssistant {...mockProps} />);
    
    unmount();
    
    expect(clearTimeoutSpy).toHaveBeenCalled();
  });
});
